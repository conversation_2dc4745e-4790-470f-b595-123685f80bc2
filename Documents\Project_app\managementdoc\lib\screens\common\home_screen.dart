import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_routes.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/document_provider.dart';
import '../../providers/category_provider.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../models/document_model.dart';
import '../admin/user_management_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  bool _dataLoaded = false;

  @override
  void initState() {
    super.initState();
    _updateSessionActivity();
  }

  // Update session activity when user is active
  void _updateSessionActivity() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.updateSessionActivity();
    });
  }

  Future<void> _loadData() async {
    if (_dataLoaded) return;

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final documentProvider = Provider.of<DocumentProvider>(
        context,
        listen: false,
      );
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );

      await Future.wait([
        userProvider.loadUsers(),
        documentProvider.loadDocuments(),
        categoryProvider.loadCategories(),
      ]);

      _dataLoaded = true;
    } catch (e) {
      print('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.currentUser == null) {
          return const PageLoadingWidget(message: 'Memuat data pengguna...');
        }

        // Load data after user is authenticated
        if (!_dataLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadData();
          });
        }

        return Scaffold(
          appBar: _buildAppBar(authProvider),
          body: _buildBody(),
          bottomNavigationBar: _buildBottomNavigationBar(),
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AuthProvider authProvider) {
    switch (_selectedIndex) {
      case 0:
        return HomeAppBar(title: AppStrings.home);
      case 1:
        return CategoryAppBar(
          title: 'Category',
          actions: [
            AssetIconAction(
              assetPath: 'assets/icon/Home.svg',
              onPressed: () {
                setState(() {
                  _selectedIndex = 0;
                });
              },
              tooltip: 'Go to Home',
            ),
          ],
        );
      case 2:
        return SimpleUploadAppBar(
          title: 'Upload Document',
          onUploadPressed: () {
            Navigator.of(context).pushNamed(AppRoutes.uploadDocument);
          },
        );
      case 3:
        return CustomAppBar(
          title: 'Add User',
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textWhite,
          actions: [
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                setState(() {
                  _selectedIndex = 0;
                });
              },
              tooltip: 'Go to Home',
            ),
          ],
        );
      case 4:
        return CustomAppBar(
          title: 'Profile',
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textWhite,
          actions: [
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                setState(() {
                  _selectedIndex = 0;
                });
              },
              tooltip: 'Go to Home',
            ),
          ],
        );
      default:
        return HomeAppBar(title: AppStrings.appName);
    }
  }

  void _showProfileMenu(AuthProvider authProvider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primaryLight,
                backgroundImage: authProvider.currentUser?.profileImage != null
                    ? NetworkImage(authProvider.currentUser!.profileImage!)
                    : null,
                child: authProvider.currentUser?.profileImage == null
                    ? Text(
                        authProvider.currentUser?.fullName
                                .substring(0, 1)
                                .toUpperCase() ??
                            'U',
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              title: Text(authProvider.currentUser?.fullName ?? ''),
              subtitle: Text(
                authProvider.currentUser?.role.toUpperCase() ?? '',
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.person_outline),
              title: const Text(AppStrings.profile),
              onTap: () {
                Navigator.pop(context);
                Navigator.of(context).pushNamed(AppRoutes.account);
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings_outlined),
              title: const Text(AppStrings.settings),
              onTap: () {
                Navigator.pop(context);
                Navigator.of(context).pushNamed(AppRoutes.settings);
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: AppColors.error),
              title: const Text(
                AppStrings.logout,
                style: TextStyle(color: AppColors.error),
              ),
              onTap: () {
                Navigator.pop(context);
                _logout();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard();
      case 1:
        return _buildFolders();
      case 2:
        return _buildUpload();
      case 3:
        return _buildUserManagement();
      case 4:
        return _buildProfile();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          color: AppColors.surface,
          child: Column(
            children: [
              // Header Section
              _buildHeader(authProvider),

              // Search Section
              _buildSearchSection(),

              // File List Section
              Expanded(child: _buildFileListSection()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(AuthProvider authProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hi ${authProvider.currentUser?.fullName != null ? authProvider.currentUser!.fullName.split(' ').first : 'John'},',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(
                'Welcome Back',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          IconButton(
            onPressed: () {
              _showProfileMenu(authProvider);
            },
            icon: const Icon(
              Icons.menu,
              color: AppColors.textPrimary,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.searchBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search your file',
                hintStyle: GoogleFonts.poppins(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.surface,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // TODO: Implement search functionality
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textWhite,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Search',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileListSection() {
    return Consumer<DocumentProvider>(
      builder: (context, documentProvider, child) {
        // Get recent documents but allow scrolling through all
        final recentDocuments = documentProvider.getRecentDocuments();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Files',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      _showFilterMenu();
                    },
                    icon: const Icon(
                      Icons.filter_list,
                      color: AppColors.textSecondary,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    tooltip: 'Filter Files',
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadow,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Table Header
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.border,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Table(
                          columnWidths: const {
                            0: FlexColumnWidth(5),
                            1: FlexColumnWidth(1),
                            2: FixedColumnWidth(41),
                          },
                          children: [
                            TableRow(
                              children: [
                                TableCell(
                                  child: Text(
                                    'Name',
                                    style: _getTableHeaderStyle(),
                                  ),
                                ),
                                TableCell(
                                  child: Text(
                                    'Date',
                                    style: _getTableHeaderStyle(),
                                  ),
                                ),
                                TableCell(
                                  child: Center(
                                    child: Text(
                                      'Action',
                                      style: _getTableHeaderStyle(),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // File List with Enhanced Scrolling
                      Expanded(
                        child: documentProvider.isLoading
                            ? const Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                ),
                              )
                            : recentDocuments.isEmpty
                            ? _buildEmptyState()
                            : Scrollbar(
                                thumbVisibility: true,
                                child: ListView.builder(
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  padding: EdgeInsets.zero,
                                  itemCount: recentDocuments.length,
                                  itemBuilder: (context, index) {
                                    final document = recentDocuments[index];
                                    return _buildDocumentRow(document);
                                  },
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No documents found',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Upload your first document to get started',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentRow(DocumentModel document) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.border, width: 0.5)),
      ),
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(5),
          1: FlexColumnWidth(1.5),
          2: FixedColumnWidth(10),
        },
        children: [
          TableRow(
            children: [
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _getFileTypeColor(
                          document.fileType,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        _getFileTypeIcon(document.fileType),
                        color: _getFileTypeColor(document.fileType),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        document.fileName,
                        style: _getTableTextStyle(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Text(
                  _formatDate(document.uploadedAt),
                  style: _getTableTextStyle(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
              TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Center(
                  child: IconButton(
                    onPressed: () => _showDocumentMenu(document),
                    icon: const Icon(
                      Icons.more_vert,
                      color: AppColors.textSecondary,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showFilterMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Files',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.access_time, color: AppColors.primary),
              title: Text('Recent', style: GoogleFonts.poppins()),
              subtitle: Text(
                'Show recently modified files',
                style: GoogleFonts.poppins(fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Filtering by recent files...')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: AppColors.error),
              title: Text('PDF Files', style: GoogleFonts.poppins()),
              subtitle: Text(
                'Show only PDF documents',
                style: GoogleFonts.poppins(fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Filtering by PDF files...')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.folder, color: AppColors.primary),
              title: Text('By Category', style: GoogleFonts.poppins()),
              subtitle: Text(
                'Filter by document category',
                style: GoogleFonts.poppins(fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Filtering by category...')),
                );
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.sort_by_alpha,
                color: AppColors.primary,
              ),
              title: Text('A-Z', style: GoogleFonts.poppins()),
              subtitle: Text(
                'Sort files alphabetically',
                style: GoogleFonts.poppins(fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Sorting files A-Z...')),
                );
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.clear, color: AppColors.textSecondary),
              title: Text('Clear Filters', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Filters cleared')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFolders() {
    // Navigate to ManageCategoryScreen when category tab is selected
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).pushNamed(AppRoutes.manageCategories).then((_) {
        // Return to home tab when coming back from categories
        setState(() {
          _selectedIndex = 0;
        });
      });
    });

    // Show loading while navigating
    return const Center(
      child: CircularProgressIndicator(color: AppColors.primary),
    );
  }

  Widget _buildUpload() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.cloud_upload, size: 80, color: AppColors.primary),
          SizedBox(height: 20),
          Text(
            'Upload Documents',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Text('Upload your documents here'),
        ],
      ),
    );
  }

  Widget _buildUserManagement() {
    // Navigate to Create User Screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).pushNamed(AppRoutes.createUser).then((_) {
        // Kembali ke home setelah selesai
        setState(() {
          _selectedIndex = 0;
        });
      });
    });

    return const Center(
      child: CircularProgressIndicator(color: AppColors.primary),
    );
  }

  Widget _buildProfile() {
    return const Center(child: Text('Profile Screen - Coming Soon'));
  }

  Widget _buildBottomNavigationBar() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        List<BottomNavigationBarItem> items = [
          BottomNavigationBarItem(
            icon: SvgPicture.asset(
              'assets/icon/Home.svg',
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                _selectedIndex == 0
                    ? AppColors.primary
                    : AppColors.textSecondary,
                BlendMode.srcIn,
              ),
            ),
            label: AppStrings.home,
          ),
          BottomNavigationBarItem(
            icon: SvgPicture.asset(
              'assets/icon/Category.svg',
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                _selectedIndex == 1
                    ? AppColors.primary
                    : AppColors.textSecondary,
                BlendMode.srcIn,
              ),
            ),
            label: 'Category',
          ),
          BottomNavigationBarItem(
            icon: Transform.translate(
              offset: const Offset(
                0,
                8,
              ), // Move up 8px without changing container size
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                    color: _selectedIndex == 2
                        ? AppColors.primary
                        : AppColors.textSecondary.withValues(alpha: 0.5),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(22),
                ),
                child: SvgPicture.asset(
                  'assets/icon/Adding.svg',
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(
                    _selectedIndex == 2
                        ? AppColors.primary
                        : AppColors.textSecondary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            label: '',
          ),
        ];

        if (authProvider.isAdmin) {
          items.addAll([
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icon/User.svg',
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  _selectedIndex == 3
                      ? AppColors.primary
                      : AppColors.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              label: 'Add User',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icon/Profile.svg',
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  _selectedIndex == 4
                      ? AppColors.primary
                      : AppColors.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              label: 'Profile',
            ),
          ]);
        }

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.textSecondary,
            showSelectedLabels: true,
            showUnselectedLabels: true,
            selectedLabelStyle: GoogleFonts.poppins(fontSize: 12),
            unselectedLabelStyle: GoogleFonts.poppins(fontSize: 12),
            iconSize: 24,
            elevation: 0,
            backgroundColor: Colors.transparent,
            items: items,
          ),
        );
      },
    );
  }

  Widget? _buildFloatingActionButton() {
    if (_selectedIndex == 2) {
      // Upload tab - FAB for uploading document
      return FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamed(AppRoutes.uploadDocument);
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.upload_file, color: AppColors.textWhite),
      );
    }
    return null;
  }

  Future<void> _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  // Helper methods for document display

  // Consistent header style for table headers
  TextStyle _getTableHeaderStyle() {
    return GoogleFonts.poppins(
      fontSize: 12,
      fontWeight: FontWeight.w600,
      color: AppColors.textSecondary,
    );
  }

  // Consistent text style for all table cells
  TextStyle _getTableTextStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.textSecondary,
    );
  }

  // Dynamic file type icon based on file type
  IconData _getFileTypeIcon(String fileType) {
    if (fileType.contains('pdf')) {
      return Icons.picture_as_pdf;
    } else if (fileType.contains('word') || fileType.contains('document')) {
      return Icons.description;
    } else if (fileType.contains('excel') || fileType.contains('spreadsheet')) {
      return Icons.table_chart;
    } else if (fileType.contains('powerpoint') ||
        fileType.contains('presentation')) {
      return Icons.slideshow;
    } else if (fileType.contains('image')) {
      return Icons.image;
    } else {
      return Icons.insert_drive_file;
    }
  }

  // Dynamic file type color based on file type
  Color _getFileTypeColor(String fileType) {
    if (fileType.contains('pdf')) {
      return AppColors.error;
    } else if (fileType.contains('word') || fileType.contains('document')) {
      return Colors.blue;
    } else if (fileType.contains('excel') || fileType.contains('spreadsheet')) {
      return Colors.green;
    } else if (fileType.contains('powerpoint') ||
        fileType.contains('presentation')) {
      return Colors.orange;
    } else if (fileType.contains('image')) {
      return Colors.purple;
    } else {
      return AppColors.textSecondary;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _showDocumentMenu(DocumentModel document) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: Text('Download', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Downloading ${document.fileName}...'),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: Text('Share', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Sharing ${document.fileName}...')),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.info_outline),
              title: Text('Details', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                _showDocumentDetails(document);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDocumentDetails(DocumentModel document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Document Details',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final user = userProvider.getUserById(document.uploadedBy);
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Name', document.fileName),
                _buildDetailRow('Owner', user?.fullName ?? 'Unknown'),
                _buildDetailRow('Size', _formatFileSize(document.fileSize)),
                _buildDetailRow('Type', document.fileType),
                _buildDetailRow('Uploaded', _formatDate(document.uploadedAt)),
                _buildDetailRow('Status', document.status.toUpperCase()),
                if (document.metadata.description.isNotEmpty)
                  _buildDetailRow('Description', document.metadata.description),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close', style: GoogleFonts.poppins()),
          ),
        ],
      ),
    );
  }
}
